export const routes = {
  main: (restaurant: string) => `/${restaurant}`,
  checkout: (restaurant: string) => `/${restaurant}/checkout`,
  thankYou: ({
    transactionId,
    restaurant,
    emailSent,
  }: {
    transactionId: string;
    restaurant: string;
    emailSent?: boolean;
  }) => {
    const params = new URLSearchParams();
    if (restaurant) params.set("restaurant", restaurant);
    if (emailSent !== undefined) params.set("emailSent", emailSent.toString());
    const queryString = params.toString();
    return `/thank-you/${transactionId}${queryString ? `?${queryString}` : ""}`;
  },
};
