import Redis from "ioredis";
import { privateConfig } from "../../privateConfig.server.ts";
import { publicConfig } from "../../publicConfig.ts";

// Mock implementation for Redis in test environments
class MockRedis {
  private storage: Map<string, { value: string; expiry: number | null }> =
    new Map();

  async get(key: string): Promise<string | null> {
    const item = this.storage.get(key);
    if (!item) return null;

    // Check if expired
    if (item.expiry !== null && item.expiry < Date.now()) {
      this.storage.delete(key);
      return null;
    }

    return item.value;
  }

  async set(
    key: string,
    value: string,
    mode?: string,
    duration?: number,
  ): Promise<"OK"> {
    let expiry: number | null = null;

    if (typeof duration === "number") {
      expiry = Date.now() + duration * 1000;
    } else if (mode !== "KEEPTTL") {
      // If not keeping TTL, reset expiry
      expiry = null;
    } else if (this.storage.has(key)) {
      // Keep existing TTL
      expiry = this.storage.get(key)?.expiry || null;
    }

    this.storage.set(key, { value, expiry });
    return "OK";
  }

  async setex(key: string, seconds: number, value: string): Promise<"OK"> {
    return this.set(key, value, undefined, seconds);
  }

  // Add any other Redis methods you need to mock here

  // Helper to clear all data (useful for test setup/teardown)
  async flushall(): Promise<"OK"> {
    this.storage.clear();
    return "OK";
  }
}

// Export either a real Redis client or a mock one
export const redis =
  publicConfig.env === "staging"
    ? (new MockRedis() as unknown as Redis)
    : new Redis(privateConfig.redisUri);
