<script lang="ts">
  import { page } from "$app/stores";
  import LeftArrowIcon from "~icons/mdi/arrow-left";
  import { routes } from "$lib/helpers/routes.ts";
  import { goto } from "$app/navigation";

  const { transactionId } = $page.params;
  const restaurant = $page.url.searchParams.get("restaurant");
  const emailSent = $page.url.searchParams.get("emailSent") !== "false";

  // const promisedTime =
  //   localStorage.getItem("orderPromisedTime") ?? "30 minutes from now";
  // localStorage.removeItem("orderPromisedTime");
</script>

<section>
  <div class="thank-you-container">
    <div class="thank-you-content">
      <h1>Thank You for Your Order!</h1>
      <div class="order-details">
        <p>Transaction ID: <span>{transactionId}</span></p>
        <!-- <p>Pickup Time: <span>{promisedTime}</span></p> -->
      </div>
      <p class="message">Your meal is being prepared and will be ready soon!</p>
      {#if emailSent}
        <p class="message">The receipt has been sent to your email.</p>
      {:else}
        <p class="message warning">
          We couldn't send your receipt via email. Please save your transaction
          ID for your records.
        </p>
      {/if}
    </div>
    {#if restaurant}
      <button
        class="button--primary-lg"
        onclick={() => goto(routes.main(restaurant))}
      >
        <LeftArrowIcon /> Back to menu</button
      >
    {/if}
  </div>
</section>

<style>
  section {
    margin-inline: auto;
    width: fit-content;
  }

  button {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--padding-sm);
    margin-block-start: var(--margin-lg);
  }

  .thank-you-container {
    background-color: var(--main-bg-color);
    padding: var(--padding-xl);
    min-height: calc(100vh - var(--min-header-height) - var(--footer-height));
  }

  .thank-you-content {
    box-shadow: var(--shadow-elevation-medium);
    border-radius: var(--radius-md);
    background-color: white;
    padding: var(--padding-xl);
    width: 100%;
    max-width: 600px;
    text-align: center;
  }

  h1 {
    margin-bottom: var(--margin-lg);
    color: var(--primary-color);
  }

  .order-details {
    margin-bottom: var(--margin-lg);
    border-radius: var(--radius-md);
    background-color: var(--gray-100);
    padding: var(--padding-lg);
  }

  .order-details p {
    font-size: var(--font-lg);
  }

  .order-details span {
    color: var(--primary-color);
    font-weight: bold;
  }

  .message {
    margin-bottom: var(--margin-md);
    font-size: var(--font-sm);
  }

  .message.warning {
    color: var(--warning-color);
    font-weight: 500;
  }

  @media (min-width: 640px) {
    .order-details p {
      font-size: var(--font-md);
    }

    .message {
      margin-bottom: 0;
      font-size: var(--font-lg);
    }
  }
</style>
