import type { Request<PERSON><PERSON><PERSON> } from "@sveltejs/kit";
import { json } from "@sveltejs/kit";
import { emailReceipt } from "$lib/api/email-receipt.server.js";
import type { ReceiptEmailInfo } from "$lib/types";
import { logger } from "$lib/logger/logger.svelte.ts";

export const POST: RequestHandler = async ({ request }) => {
  try {
    const receiptEmailInfo = (await request.json()) as ReceiptEmailInfo;

    await emailReceipt(receiptEmailInfo);

    return json({ success: true });
  } catch (emailError) {
    logger.error({ error: emailError }, "Failed to send email receipt");

    // Return a 200 status with success: false instead of throwing an error
    // This allows the client to handle email failures gracefully
    return json({
      success: false,
      error: emailError instanceof Error ? emailError.message : "Unknown error"
    });
  }
};
